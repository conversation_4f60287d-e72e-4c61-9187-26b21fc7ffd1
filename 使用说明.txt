微信防检测工具 - 使用说明
================================

【重要警告】
此工具专为阿里云服务器设计，用于规避微信的虚拟机检测。
使用前请确保您了解相关风险并遵守法律法规。

【编译方法】
方法一：使用CMake（推荐）
1. 确保安装了Visual Studio 2019或更高版本
2. 安装CMake 3.10或更高版本
3. 双击运行 build.bat
4. 等待编译完成

方法二：简单编译
1. 打开 "Developer Command Prompt for VS"
2. 进入项目目录
3. 运行 compile_simple.bat
4. 等待编译完成

【使用步骤】
1. 将所有文件上传到阿里云服务器
2. 编译程序
3. 以管理员身份运行 WeChatAntiDetection.exe
4. 按照菜单提示操作：
   - 首先选择 "1. 初始化系统"
   - 然后选择 "2. 应用防检测措施"
   - 可选择 "5. 运行检测测试" 验证效果

【功能说明】
1. 初始化系统 - 备份当前系统信息，必须首先执行
2. 应用防检测措施 - 修改系统信息以隐藏虚拟化特征
3. 恢复原始设置 - 恢复到修改前的状态
4. 查看当前状态 - 显示当前系统信息
5. 运行检测测试 - 测试防检测效果
6. 查看检测结果 - 显示详细检测信息

【主要修改内容】
- CPU信息：修改为真实的Intel/AMD处理器型号
- 主板信息：更改为ASUS、MSI等真实品牌
- BIOS信息：隐藏虚拟化相关标识
- MAC地址：生成真实网卡厂商的MAC地址
- 系统服务：停止虚拟化相关服务
- 注册表：清理虚拟化痕迹

【注意事项】
1. 必须以管理员身份运行
2. 建议在应用防检测措施后重启系统
3. 程序会生成日志文件 anti_detection_log.txt
4. 如需恢复原始设置，请使用程序内的恢复功能
5. 使用前请备份重要数据

【故障排除】
- 如果提示权限不足，请确保以管理员身份运行
- 如果编译失败，请检查Visual Studio安装
- 如果某些功能失效，请查看日志文件
- 如果需要完全恢复，可以重装系统

【技术原理】
本工具通过修改Windows注册表中的硬件信息，隐藏虚拟化特征，
使系统看起来像运行在真实物理机上，从而规避微信的虚拟机检测。

【免责声明】
本工具仅供学习研究使用，使用者需自行承担风险。
开发者不对因使用本工具造成的任何损失负责。

版本：v1.0
更新时间：2025年1月
