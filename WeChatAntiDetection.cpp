#include "WeChatAntiDetection.h"
#include <winreg.h>
#include <iphlpapi.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <tlhelp32.h>
#include <psapi.h>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "advapi32.lib")

// 常量定义
namespace AntiDetectionConstants {
    const std::string HARDWARE_KEY = "HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0";
    const std::string BIOS_KEY = "HARDWARE\\DESCRIPTION\\System\\BIOS";
    const std::string SYSTEM_KEY = "HARDWARE\\DESCRIPTION\\System";
    const std::string NETWORK_KEY = "SYSTEM\\CurrentControlSet\\Control\\Class\\{4D36E972-E325-11CE-BFC1-08002BE10318}";

    const std::vector<std::string> VM_KEYWORDS = {
        "VMware", "VirtualBox", "QEMU", "KVM", "Xen", "Hyper-V",
        "Virtual", "VM", "vbox", "vmware", "qemu", "kvm", "xen"
    };

    const std::vector<std::string> CLOUD_KEYWORDS = {
        "Alibaba", "Aliyun", "Amazon", "AWS", "Google", "Azure",
        "Tencent", "Baidu", "Huawei", "Cloud"
    };

    const std::vector<std::string> FAKE_CPU_NAMES = {
        "Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz",
        "Intel(R) Core(TM) i5-9600K CPU @ 3.70GHz",
        "AMD Ryzen 7 3700X 8-Core Processor",
        "Intel(R) Core(TM) i9-9900K CPU @ 3.60GHz",
        "Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz",
        "AMD Ryzen 5 3600 6-Core Processor"
    };

    const std::vector<std::string> FAKE_MOTHERBOARD_MODELS = {
        "ASUS PRIME Z390-A",
        "MSI B450 TOMAHAWK MAX",
        "GIGABYTE Z390 AORUS PRO",
        "ASRock B450M PRO4",
        "ASUS ROG STRIX Z390-E GAMING",
        "MSI Z390-A PRO"
    };
}

WeChatAntiDetection::WeChatAntiDetection() : isApplied(false) {
    logFile = "anti_detection_log.txt";
    WriteLog("WeChatAntiDetection initialized");
}

WeChatAntiDetection::~WeChatAntiDetection() {
    WriteLog("WeChatAntiDetection destroyed");
}

bool WeChatAntiDetection::Initialize() {
    WriteLog("Initializing anti-detection system...");

    if (!IsRunningAsAdmin()) {
        WriteLog("ERROR: Administrator privileges required");
        return false;
    }

    // 备份当前系统信息
    WriteLog("Backing up current system information...");

    // 备份关键注册表项
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::HARDWARE_KEY, "ProcessorNameString");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemManufacturer");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemProductName");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "BIOSVersion");

    WriteLog("Initialization completed successfully");
    return true;
}

bool WeChatAntiDetection::ApplyAntiDetection() {
    WriteLog("Applying anti-detection measures...");

    bool success = true;

    // 1. 修改CPU信息
    WriteLog("Modifying CPU information...");
    if (!ModifyCPUInfo()) {
        WriteLog("WARNING: Failed to modify CPU information");
        success = false;
    }

    // 2. 修改主板信息
    WriteLog("Modifying motherboard information...");
    if (!ModifyMotherboardInfo()) {
        WriteLog("WARNING: Failed to modify motherboard information");
        success = false;
    }

    // 3. 修改BIOS信息
    WriteLog("Modifying BIOS information...");
    if (!ModifyBIOSInfo()) {
        WriteLog("WARNING: Failed to modify BIOS information");
        success = false;
    }

    // 4. 修改MAC地址信息
    WriteLog("Modifying MAC address information...");
    if (!ModifyMACAddress()) {
        WriteLog("WARNING: Failed to modify MAC address information");
        success = false;
    }

    // 5. 隐藏虚拟化特征
    WriteLog("Hiding virtualization features...");
    if (!HideVirtualizationFeatures()) {
        WriteLog("WARNING: Failed to hide virtualization features");
        success = false;
    }

    // 6. 停止虚拟化相关服务
    WriteLog("Stopping virtualization services...");
    if (!StopVirtualizationServices()) {
        WriteLog("WARNING: Failed to stop virtualization services");
        success = false;
    }

    // 7. 清理虚拟化痕迹
    WriteLog("Cleaning virtualization traces...");
    if (!CleanVirtualizationTraces()) {
        WriteLog("WARNING: Failed to clean virtualization traces");
        success = false;
    }

    if (success) {
        isApplied = true;
        WriteLog("Anti-detection measures applied successfully");
    } else {
        WriteLog("Some anti-detection measures failed to apply");
    }

    return success;
}

bool WeChatAntiDetection::ModifyCPUInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_CPU_NAMES.size() - 1);

    std::string fakeCPUName = AntiDetectionConstants::FAKE_CPU_NAMES[dis(gen)];

    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           AntiDetectionConstants::HARDWARE_KEY,
                           "ProcessorNameString",
                           fakeCPUName);
}

bool WeChatAntiDetection::ModifyMotherboardInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS.size() - 1);

    std::string fakeMotherboard = AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS[dis(gen)];

    bool success = true;
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               AntiDetectionConstants::BIOS_KEY,
                               "SystemProductName",
                               fakeMotherboard);

    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               AntiDetectionConstants::BIOS_KEY,
                               "SystemManufacturer",
                               "ASUS");

    return success;
}

bool WeChatAntiDetection::ModifyBIOSInfo() {
    std::string fakeBIOSVersion = "American Megatrends Inc. " + GenerateRandomString(8);

    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           AntiDetectionConstants::BIOS_KEY,
                           "BIOSVersion",
                           fakeBIOSVersion);
}

bool WeChatAntiDetection::ModifyMACAddress() {
    // 生成一个非虚拟化厂商的MAC地址
    std::string fakeMACAddress = GenerateRandomMACAddress();

    // 修改网络适配器的MAC地址显示
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                               AntiDetectionConstants::NETWORK_KEY.c_str(),
                               0, KEY_READ, &hKey);

    if (result != ERROR_SUCCESS) {
        return false;
    }

    // 枚举网络适配器
    DWORD index = 0;
    char subKeyName[256];
    DWORD subKeyNameSize = sizeof(subKeyName);

    while (RegEnumKeyExA(hKey, index, subKeyName, &subKeyNameSize,
                        NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {

        std::string fullPath = AntiDetectionConstants::NETWORK_KEY + "\\" + subKeyName;
        SetRegistryValue(HKEY_LOCAL_MACHINE, fullPath, "NetworkAddress", fakeMACAddress);

        index++;
        subKeyNameSize = sizeof(subKeyName);
    }

    RegCloseKey(hKey);
    return true;
}

bool WeChatAntiDetection::HideVirtualizationFeatures() {
    bool success = true;

    // 隐藏KVM特征
    success &= HideKVMFeatures();

    // 隐藏Xen特征
    success &= HideXenFeatures();

    // 隐藏VMware特征
    success &= HideVMwareFeatures();

    // 隐藏VirtualBox特征
    success &= HideVirtualBoxFeatures();

    // 隐藏Hyper-V特征
    success &= HideHyperVFeatures();

    return success;
}

bool WeChatAntiDetection::HideKVMFeatures() {
    // 修改CPUID信息以隐藏KVM特征
    bool success = true;

    // 移除KVM相关的注册表项
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\kvm",
                               "Start", "4"); // 禁用

    // 隐藏KVM相关的设备
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_KVM",
                               "NextInstance", "0");

    return success;
}

bool WeChatAntiDetection::HideXenFeatures() {
    // 隐藏Xen虚拟化特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\xenbus",
                           "Start", "4");
}

bool WeChatAntiDetection::HideVMwareFeatures() {
    bool success = true;

    // 隐藏VMware工具和服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmtools",
                               "Start", "4");

    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmmouse",
                               "Start", "4");

    return success;
}

bool WeChatAntiDetection::HideVirtualBoxFeatures() {
    bool success = true;

    // 隐藏VirtualBox相关服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxGuest",
                               "Start", "4");

    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxMouse",
                               "Start", "4");

    return success;
}

bool WeChatAntiDetection::HideHyperVFeatures() {
    // 隐藏Hyper-V特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\vmbus",
                           "Start", "4");
}

bool WeChatAntiDetection::StopVirtualizationServices() {
    std::vector<std::string> services = {
        "vmtools", "vmmouse", "VBoxGuest", "VBoxMouse",
        "VBoxService", "xenbus", "kvm", "vmbus"
    };

    bool success = true;
    for (const auto& service : services) {
        SC_HANDLE scManager = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
        if (scManager) {
            SC_HANDLE scService = OpenServiceA(scManager, service.c_str(), SERVICE_STOP | SERVICE_QUERY_STATUS);
            if (scService) {
                SERVICE_STATUS status;
                ControlService(scService, SERVICE_CONTROL_STOP, &status);
                CloseServiceHandle(scService);
            }
            CloseServiceHandle(scManager);
        }
    }

    return success;
}

bool WeChatAntiDetection::CleanVirtualizationTraces() {
    bool success = true;

    // 清理虚拟化相关的注册表项
    std::vector<std::string> keysToClean = {
        "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_VMWARE",
        "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_VBOX",
        "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_KVM",
        "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_XEN"
    };

    for (const auto& key : keysToClean) {
        RegDeleteKeyA(HKEY_LOCAL_MACHINE, key.c_str());
    }

    // 修改系统制造商信息
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "HARDWARE\\DESCRIPTION\\System\\BIOS",
                               "SystemManufacturer", "Dell Inc.");

    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "HARDWARE\\DESCRIPTION\\System\\BIOS",
                               "SystemProductName", "OptiPlex 7070");

    return success;
}

bool WeChatAntiDetection::SetRegistryValue(HKEY hKey, const std::string& subKey,
                                          const std::string& valueName, const std::string& value) {
    HKEY hSubKey;
    LONG result = RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_SET_VALUE, &hSubKey);

    if (result != ERROR_SUCCESS) {
        // 尝试创建键
        result = RegCreateKeyExA(hKey, subKey.c_str(), 0, NULL, REG_OPTION_NON_VOLATILE,
                                KEY_SET_VALUE, NULL, &hSubKey, NULL);
        if (result != ERROR_SUCCESS) {
            return false;
        }
    }

    result = RegSetValueExA(hSubKey, valueName.c_str(), 0, REG_SZ,
                           (const BYTE*)value.c_str(), value.length() + 1);

    RegCloseKey(hSubKey);
    return result == ERROR_SUCCESS;
}

std::string WeChatAntiDetection::GetRegistryValue(HKEY hKey, const std::string& subKey,
                                                 const std::string& valueName) {
    HKEY hSubKey;
    LONG result = RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey);

    if (result != ERROR_SUCCESS) {
        return "";
    }

    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;

    result = RegQueryValueExA(hSubKey, valueName.c_str(), NULL, &type,
                             (BYTE*)buffer, &bufferSize);

    RegCloseKey(hSubKey);

    if (result == ERROR_SUCCESS && type == REG_SZ) {
        return std::string(buffer);
    }

    return "";
}

bool WeChatAntiDetection::BackupRegistryValue(HKEY hKey, const std::string& subKey,
                                             const std::string& valueName) {
    std::string value = GetRegistryValue(hKey, subKey, valueName);
    if (!value.empty()) {
        std::string fullKey = subKey + "\\" + valueName;
        backupData.registryBackup[fullKey] = value;
        return true;
    }
    return false;
}

std::string WeChatAntiDetection::GenerateRandomString(int length) {
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, chars.size() - 1);

    std::string result;
    for (int i = 0; i < length; ++i) {
        result += chars[dis(gen)];
    }
    return result;
}

std::string WeChatAntiDetection::GenerateRandomMACAddress() {
    // 生成一个看起来像真实硬件的MAC地址
    // 使用Intel、Realtek等真实厂商的OUI前缀
    std::vector<std::string> realOUIs = {
        "00:1B:21", "00:1E:68", "00:22:4D", "00:26:B9", // Intel
        "00:E0:4C", "52:54:00", "08:00:27", "00:15:5D"  // Realtek等
    };

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> ouiDis(0, realOUIs.size() - 1);
    std::uniform_int_distribution<> hexDis(0, 255);

    std::string oui = realOUIs[ouiDis(gen)];

    std::stringstream ss;
    ss << oui << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen);

    return ss.str();
}

bool WeChatAntiDetection::IsRunningAsAdmin() {
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;

    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }

    return isAdmin == TRUE;
}

void WeChatAntiDetection::WriteLog(const std::string& message) {
    std::ofstream logStream(logFile, std::ios::app);
    if (logStream.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        logStream << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] "
                  << message << std::endl;
        logStream.close();
    }
}

bool WeChatAntiDetection::RestoreOriginalSettings() {
    WriteLog("Restoring original settings...");

    bool success = true;

    // 恢复注册表备份
    for (const auto& backup : backupData.registryBackup) {
        size_t pos = backup.first.find_last_of('\\');
        if (pos != std::string::npos) {
            std::string subKey = backup.first.substr(0, pos);
            std::string valueName = backup.first.substr(pos + 1);

            if (!SetRegistryValue(HKEY_LOCAL_MACHINE, subKey, valueName, backup.second)) {
                WriteLog("Failed to restore: " + backup.first);
                success = false;
            }
        }
    }

    if (success) {
        isApplied = false;
        WriteLog("Original settings restored successfully");
    } else {
        WriteLog("Some settings failed to restore");
    }

    return success;
}

bool WeChatAntiDetection::IsAntiDetectionApplied() const {
    return isApplied;
}

std::vector<std::string> WeChatAntiDetection::GetCurrentStatus() {
    std::vector<std::string> status;

    status.push_back("Anti-detection status: " + std::string(isApplied ? "Applied" : "Not Applied"));

    // 检查当前CPU信息
    std::string currentCPU = GetRegistryValue(HKEY_LOCAL_MACHINE,
                                             AntiDetectionConstants::HARDWARE_KEY,
                                             "ProcessorNameString");
    status.push_back("Current CPU: " + currentCPU);

    // 检查当前系统制造商
    std::string currentManufacturer = GetRegistryValue(HKEY_LOCAL_MACHINE,
                                                      AntiDetectionConstants::BIOS_KEY,
                                                      "SystemManufacturer");
    status.push_back("Current Manufacturer: " + currentManufacturer);

    return status;
}

bool WeChatAntiDetection::RunDetectionTest() {
    WriteLog("Running detection test...");

    std::vector<std::string> indicators = GetVirtualizationIndicators();

    WriteLog("Detection test completed. Found " + std::to_string(indicators.size()) + " indicators");

    return indicators.empty();
}

std::vector<std::string> WeChatAntiDetection::GetVirtualizationIndicators() {
    std::vector<std::string> indicators;

    // 检查CPU名称
    std::string cpuName = GetRegistryValue(HKEY_LOCAL_MACHINE,
                                          AntiDetectionConstants::HARDWARE_KEY,
                                          "ProcessorNameString");

    for (const auto& keyword : AntiDetectionConstants::VM_KEYWORDS) {
        if (cpuName.find(keyword) != std::string::npos) {
            indicators.push_back("CPU contains VM keyword: " + keyword);
        }
    }

    // 检查系统制造商
    std::string manufacturer = GetRegistryValue(HKEY_LOCAL_MACHINE,
                                               AntiDetectionConstants::BIOS_KEY,
                                               "SystemManufacturer");

    for (const auto& keyword : AntiDetectionConstants::CLOUD_KEYWORDS) {
        if (manufacturer.find(keyword) != std::string::npos) {
            indicators.push_back("Manufacturer contains cloud keyword: " + keyword);
        }
    }

    return indicators;
}

std::vector<std::string> WeChatAntiDetection::GetDetectionResults() {
    return GetVirtualizationIndicators();
}

std::string WeChatAntiDetection::GetLastError() const {
    return "Check log file for detailed error information";
}

bool WeChatAntiDetection::Initialize() {
    WriteLog("Initializing anti-detection system...");
    
    if (!IsRunningAsAdmin()) {
        WriteLog("ERROR: Administrator privileges required");
        return false;
    }
    
    // 备份当前系统信息
    WriteLog("Backing up current system information...");
    
    // 备份关键注册表项
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::HARDWARE_KEY, "ProcessorNameString");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemManufacturer");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemProductName");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "BIOSVersion");
    
    WriteLog("Initialization completed successfully");
    return true;
}

bool WeChatAntiDetection::ApplyAntiDetection() {
    WriteLog("Applying anti-detection measures...");
    
    bool success = true;
    
    // 1. 修改CPU信息
    WriteLog("Modifying CPU information...");
    if (!ModifyCPUInfo()) {
        WriteLog("WARNING: Failed to modify CPU information");
        success = false;
    }
    
    // 2. 修改主板信息
    WriteLog("Modifying motherboard information...");
    if (!ModifyMotherboardInfo()) {
        WriteLog("WARNING: Failed to modify motherboard information");
        success = false;
    }
    
    // 3. 修改BIOS信息
    WriteLog("Modifying BIOS information...");
    if (!ModifyBIOSInfo()) {
        WriteLog("WARNING: Failed to modify BIOS information");
        success = false;
    }
    
    // 4. 修改MAC地址信息
    WriteLog("Modifying MAC address information...");
    if (!ModifyMACAddress()) {
        WriteLog("WARNING: Failed to modify MAC address information");
        success = false;
    }
    
    // 5. 隐藏虚拟化特征
    WriteLog("Hiding virtualization features...");
    if (!HideVirtualizationFeatures()) {
        WriteLog("WARNING: Failed to hide virtualization features");
        success = false;
    }
    
    // 6. 停止虚拟化相关服务
    WriteLog("Stopping virtualization services...");
    if (!StopVirtualizationServices()) {
        WriteLog("WARNING: Failed to stop virtualization services");
        success = false;
    }
    
    // 7. 清理虚拟化痕迹
    WriteLog("Cleaning virtualization traces...");
    if (!CleanVirtualizationTraces()) {
        WriteLog("WARNING: Failed to clean virtualization traces");
        success = false;
    }
    
    if (success) {
        isApplied = true;
        WriteLog("Anti-detection measures applied successfully");
    } else {
        WriteLog("Some anti-detection measures failed to apply");
    }
    
    return success;
}

bool WeChatAntiDetection::ModifyCPUInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_CPU_NAMES.size() - 1);
    
    std::string fakeCPUName = AntiDetectionConstants::FAKE_CPU_NAMES[dis(gen)];
    
    return SetRegistryValue(HKEY_LOCAL_MACHINE, 
                           AntiDetectionConstants::HARDWARE_KEY, 
                           "ProcessorNameString", 
                           fakeCPUName);
}

bool WeChatAntiDetection::ModifyMotherboardInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS.size() - 1);
    
    std::string fakeMotherboard = AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS[dis(gen)];
    
    bool success = true;
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::BIOS_KEY, 
                               "SystemProductName", 
                               fakeMotherboard);
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::BIOS_KEY, 
                               "SystemManufacturer", 
                               "ASUS");
    
    return success;
}

bool WeChatAntiDetection::ModifyBIOSInfo() {
    std::string fakeBIOSVersion = "American Megatrends Inc. " + GenerateRandomString(8);
    
    return SetRegistryValue(HKEY_LOCAL_MACHINE, 
                           AntiDetectionConstants::BIOS_KEY, 
                           "BIOSVersion", 
                           fakeBIOSVersion);
}

bool WeChatAntiDetection::ModifyMACAddress() {
    // 生成一个非虚拟化厂商的MAC地址
    std::string fakeMACAddress = GenerateRandomMACAddress();
    
    // 修改网络适配器的MAC地址显示
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::NETWORK_KEY.c_str(), 
                               0, KEY_READ, &hKey);
    
    if (result != ERROR_SUCCESS) {
        return false;
    }
    
    // 枚举网络适配器
    DWORD index = 0;
    char subKeyName[256];
    DWORD subKeyNameSize = sizeof(subKeyName);
    
    while (RegEnumKeyExA(hKey, index, subKeyName, &subKeyNameSize, 
                        NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {
        
        std::string fullPath = AntiDetectionConstants::NETWORK_KEY + "\\" + subKeyName;
        SetRegistryValue(HKEY_LOCAL_MACHINE, fullPath, "NetworkAddress", fakeMACAddress);
        
        index++;
        subKeyNameSize = sizeof(subKeyName);
    }
    
    RegCloseKey(hKey);
    return true;
}

bool WeChatAntiDetection::HideVirtualizationFeatures() {
    bool success = true;
    
    // 隐藏KVM特征
    success &= HideKVMFeatures();
    
    // 隐藏Xen特征
    success &= HideXenFeatures();
    
    // 隐藏VMware特征
    success &= HideVMwareFeatures();
    
    // 隐藏VirtualBox特征
    success &= HideVirtualBoxFeatures();
    
    // 隐藏Hyper-V特征
    success &= HideHyperVFeatures();
    
    return success;
}

bool WeChatAntiDetection::HideKVMFeatures() {
    // 修改CPUID信息以隐藏KVM特征
    bool success = true;
    
    // 移除KVM相关的注册表项
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               "SYSTEM\\CurrentControlSet\\Services\\kvm", 
                               "Start", "4"); // 禁用
    
    // 隐藏KVM相关的设备
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_KVM",
                               "NextInstance", "0");
    
    return success;
}

bool WeChatAntiDetection::HideXenFeatures() {
    // 隐藏Xen虚拟化特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\xenbus",
                           "Start", "4");
}

bool WeChatAntiDetection::HideVMwareFeatures() {
    bool success = true;
    
    // 隐藏VMware工具和服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmtools",
                               "Start", "4");
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmmouse",
                               "Start", "4");
    
    return success;
}

bool WeChatAntiDetection::HideVirtualBoxFeatures() {
    bool success = true;
    
    // 隐藏VirtualBox相关服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxGuest",
                               "Start", "4");
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxMouse",
                               "Start", "4");
    
    return success;
}

bool WeChatAntiDetection::HideHyperVFeatures() {
    // 隐藏Hyper-V特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\vmbus",
                           "Start", "4");
}

bool WeChatAntiDetection::StopVirtualizationServices() {
    std::vector<std::string> services = {
        "vmtools", "vmmouse", "VBoxGuest", "VBoxMouse", 
        "VBoxService", "xenbus", "kvm", "vmbus"
    };
    
    bool success = true;
    for (const auto& service : services) {
        SC_HANDLE scManager = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
        if (scManager) {
            SC_HANDLE scService = OpenServiceA(scManager, service.c_str(), SERVICE_STOP | SERVICE_QUERY_STATUS);
            if (scService) {
                SERVICE_STATUS status;
                ControlService(scService, SERVICE_CONTROL_STOP, &status);
                CloseServiceHandle(scService);
            }
            CloseServiceHandle(scManager);
        }
    }
    
    return success;
}

std::string WeChatAntiDetection::GenerateRandomString(int length) {
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, chars.size() - 1);
    
    std::string result;
    for (int i = 0; i < length; ++i) {
        result += chars[dis(gen)];
    }
    return result;
}

std::string WeChatAntiDetection::GenerateRandomMACAddress() {
    // 生成一个看起来像真实硬件的MAC地址
    // 使用Intel、Realtek等真实厂商的OUI前缀
    std::vector<std::string> realOUIs = {
        "00:1B:21", "00:1E:68", "00:22:4D", "00:26:B9", // Intel
        "00:E0:4C", "52:54:00", "08:00:27", "00:15:5D"  // Realtek等
    };
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> ouiDis(0, realOUIs.size() - 1);
    std::uniform_int_distribution<> hexDis(0, 255);
    
    std::string oui = realOUIs[ouiDis(gen)];
    
    std::stringstream ss;
    ss << oui << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen);
    
    return ss.str();
}
