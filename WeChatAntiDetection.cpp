#include "WeChatAntiDetection.h"
#include <winreg.h>
#include <iphlpapi.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <tlhelp32.h>
#include <psapi.h>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "advapi32.lib")

WeChatAntiDetection::WeChatAntiDetection() : isApplied(false) {
    logFile = "anti_detection_log.txt";
    WriteLog("WeChatAntiDetection initialized");
}

WeChatAntiDetection::~WeChatAntiDetection() {
    WriteLog("WeChatAntiDetection destroyed");
}

bool WeChatAntiDetection::Initialize() {
    WriteLog("Initializing anti-detection system...");
    
    if (!IsRunningAsAdmin()) {
        WriteLog("ERROR: Administrator privileges required");
        return false;
    }
    
    // 备份当前系统信息
    WriteLog("Backing up current system information...");
    
    // 备份关键注册表项
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::HARDWARE_KEY, "ProcessorNameString");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemManufacturer");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "SystemProductName");
    BackupRegistryValue(HKEY_LOCAL_MACHINE, AntiDetectionConstants::BIOS_KEY, "BIOSVersion");
    
    WriteLog("Initialization completed successfully");
    return true;
}

bool WeChatAntiDetection::ApplyAntiDetection() {
    WriteLog("Applying anti-detection measures...");
    
    bool success = true;
    
    // 1. 修改CPU信息
    WriteLog("Modifying CPU information...");
    if (!ModifyCPUInfo()) {
        WriteLog("WARNING: Failed to modify CPU information");
        success = false;
    }
    
    // 2. 修改主板信息
    WriteLog("Modifying motherboard information...");
    if (!ModifyMotherboardInfo()) {
        WriteLog("WARNING: Failed to modify motherboard information");
        success = false;
    }
    
    // 3. 修改BIOS信息
    WriteLog("Modifying BIOS information...");
    if (!ModifyBIOSInfo()) {
        WriteLog("WARNING: Failed to modify BIOS information");
        success = false;
    }
    
    // 4. 修改MAC地址信息
    WriteLog("Modifying MAC address information...");
    if (!ModifyMACAddress()) {
        WriteLog("WARNING: Failed to modify MAC address information");
        success = false;
    }
    
    // 5. 隐藏虚拟化特征
    WriteLog("Hiding virtualization features...");
    if (!HideVirtualizationFeatures()) {
        WriteLog("WARNING: Failed to hide virtualization features");
        success = false;
    }
    
    // 6. 停止虚拟化相关服务
    WriteLog("Stopping virtualization services...");
    if (!StopVirtualizationServices()) {
        WriteLog("WARNING: Failed to stop virtualization services");
        success = false;
    }
    
    // 7. 清理虚拟化痕迹
    WriteLog("Cleaning virtualization traces...");
    if (!CleanVirtualizationTraces()) {
        WriteLog("WARNING: Failed to clean virtualization traces");
        success = false;
    }
    
    if (success) {
        isApplied = true;
        WriteLog("Anti-detection measures applied successfully");
    } else {
        WriteLog("Some anti-detection measures failed to apply");
    }
    
    return success;
}

bool WeChatAntiDetection::ModifyCPUInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_CPU_NAMES.size() - 1);
    
    std::string fakeCPUName = AntiDetectionConstants::FAKE_CPU_NAMES[dis(gen)];
    
    return SetRegistryValue(HKEY_LOCAL_MACHINE, 
                           AntiDetectionConstants::HARDWARE_KEY, 
                           "ProcessorNameString", 
                           fakeCPUName);
}

bool WeChatAntiDetection::ModifyMotherboardInfo() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS.size() - 1);
    
    std::string fakeMotherboard = AntiDetectionConstants::FAKE_MOTHERBOARD_MODELS[dis(gen)];
    
    bool success = true;
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::BIOS_KEY, 
                               "SystemProductName", 
                               fakeMotherboard);
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::BIOS_KEY, 
                               "SystemManufacturer", 
                               "ASUS");
    
    return success;
}

bool WeChatAntiDetection::ModifyBIOSInfo() {
    std::string fakeBIOSVersion = "American Megatrends Inc. " + GenerateRandomString(8);
    
    return SetRegistryValue(HKEY_LOCAL_MACHINE, 
                           AntiDetectionConstants::BIOS_KEY, 
                           "BIOSVersion", 
                           fakeBIOSVersion);
}

bool WeChatAntiDetection::ModifyMACAddress() {
    // 生成一个非虚拟化厂商的MAC地址
    std::string fakeMACAddress = GenerateRandomMACAddress();
    
    // 修改网络适配器的MAC地址显示
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
                               AntiDetectionConstants::NETWORK_KEY.c_str(), 
                               0, KEY_READ, &hKey);
    
    if (result != ERROR_SUCCESS) {
        return false;
    }
    
    // 枚举网络适配器
    DWORD index = 0;
    char subKeyName[256];
    DWORD subKeyNameSize = sizeof(subKeyName);
    
    while (RegEnumKeyExA(hKey, index, subKeyName, &subKeyNameSize, 
                        NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {
        
        std::string fullPath = AntiDetectionConstants::NETWORK_KEY + "\\" + subKeyName;
        SetRegistryValue(HKEY_LOCAL_MACHINE, fullPath, "NetworkAddress", fakeMACAddress);
        
        index++;
        subKeyNameSize = sizeof(subKeyName);
    }
    
    RegCloseKey(hKey);
    return true;
}

bool WeChatAntiDetection::HideVirtualizationFeatures() {
    bool success = true;
    
    // 隐藏KVM特征
    success &= HideKVMFeatures();
    
    // 隐藏Xen特征
    success &= HideXenFeatures();
    
    // 隐藏VMware特征
    success &= HideVMwareFeatures();
    
    // 隐藏VirtualBox特征
    success &= HideVirtualBoxFeatures();
    
    // 隐藏Hyper-V特征
    success &= HideHyperVFeatures();
    
    return success;
}

bool WeChatAntiDetection::HideKVMFeatures() {
    // 修改CPUID信息以隐藏KVM特征
    bool success = true;
    
    // 移除KVM相关的注册表项
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE, 
                               "SYSTEM\\CurrentControlSet\\Services\\kvm", 
                               "Start", "4"); // 禁用
    
    // 隐藏KVM相关的设备
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Enum\\Root\\LEGACY_KVM",
                               "NextInstance", "0");
    
    return success;
}

bool WeChatAntiDetection::HideXenFeatures() {
    // 隐藏Xen虚拟化特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\xenbus",
                           "Start", "4");
}

bool WeChatAntiDetection::HideVMwareFeatures() {
    bool success = true;
    
    // 隐藏VMware工具和服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmtools",
                               "Start", "4");
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\vmmouse",
                               "Start", "4");
    
    return success;
}

bool WeChatAntiDetection::HideVirtualBoxFeatures() {
    bool success = true;
    
    // 隐藏VirtualBox相关服务
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxGuest",
                               "Start", "4");
    
    success &= SetRegistryValue(HKEY_LOCAL_MACHINE,
                               "SYSTEM\\CurrentControlSet\\Services\\VBoxMouse",
                               "Start", "4");
    
    return success;
}

bool WeChatAntiDetection::HideHyperVFeatures() {
    // 隐藏Hyper-V特征
    return SetRegistryValue(HKEY_LOCAL_MACHINE,
                           "SYSTEM\\CurrentControlSet\\Services\\vmbus",
                           "Start", "4");
}

bool WeChatAntiDetection::StopVirtualizationServices() {
    std::vector<std::string> services = {
        "vmtools", "vmmouse", "VBoxGuest", "VBoxMouse", 
        "VBoxService", "xenbus", "kvm", "vmbus"
    };
    
    bool success = true;
    for (const auto& service : services) {
        SC_HANDLE scManager = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
        if (scManager) {
            SC_HANDLE scService = OpenServiceA(scManager, service.c_str(), SERVICE_STOP | SERVICE_QUERY_STATUS);
            if (scService) {
                SERVICE_STATUS status;
                ControlService(scService, SERVICE_CONTROL_STOP, &status);
                CloseServiceHandle(scService);
            }
            CloseServiceHandle(scManager);
        }
    }
    
    return success;
}

std::string WeChatAntiDetection::GenerateRandomString(int length) {
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, chars.size() - 1);
    
    std::string result;
    for (int i = 0; i < length; ++i) {
        result += chars[dis(gen)];
    }
    return result;
}

std::string WeChatAntiDetection::GenerateRandomMACAddress() {
    // 生成一个看起来像真实硬件的MAC地址
    // 使用Intel、Realtek等真实厂商的OUI前缀
    std::vector<std::string> realOUIs = {
        "00:1B:21", "00:1E:68", "00:22:4D", "00:26:B9", // Intel
        "00:E0:4C", "52:54:00", "08:00:27", "00:15:5D"  // Realtek等
    };
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> ouiDis(0, realOUIs.size() - 1);
    std::uniform_int_distribution<> hexDis(0, 255);
    
    std::string oui = realOUIs[ouiDis(gen)];
    
    std::stringstream ss;
    ss << oui << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen) << ":";
    ss << std::hex << std::uppercase << hexDis(gen);
    
    return ss.str();
}
