@echo off
chcp 65001 >nul
echo 自动安装MinGW编译环境...

REM 创建临时目录
if not exist temp mkdir temp
cd temp

echo 正在下载MinGW-w64...
echo 这可能需要几分钟时间...

REM 使用PowerShell下载MinGW
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/niXman/mingw-builds-binaries/releases/download/13.2.0-rt_v11-rev0/x86_64-13.2.0-release-win32-seh-msvcrt-rt_v11-rev0.7z' -OutFile 'mingw.7z'}"

if not exist mingw.7z (
    echo 下载失败！请手动下载MinGW-w64
    echo 下载地址: https://github.com/niXman/mingw-builds-binaries/releases
    pause
    exit /b 1
)

echo 正在解压...
REM 使用PowerShell解压（Windows 10+自带）
powershell -Command "Expand-Archive -Path 'mingw.7z' -DestinationPath 'C:\' -Force"

if not exist "C:\mingw64\bin\g++.exe" (
    echo 解压失败！请手动解压mingw.7z到C:\
    pause
    exit /b 1
)

echo 正在设置环境变量...
REM 添加到PATH
setx PATH "%PATH%;C:\mingw64\bin" /M

echo.
echo 安装完成！
echo MinGW已安装到: C:\mingw64
echo.
echo 请重新打开命令提示符，然后运行:
echo compile_mingw.bat
echo.
pause

cd ..
rmdir /s /q temp
