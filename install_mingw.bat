@echo off
echo Auto-installing MinGW compilation environment...

REM Create temporary directory
if not exist temp mkdir temp
cd temp

echo Downloading MinGW-w64...
echo This may take a few minutes...

REM Download MinGW using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/niXman/mingw-builds-binaries/releases/download/13.2.0-rt_v11-rev0/x86_64-13.2.0-release-win32-seh-msvcrt-rt_v11-rev0.7z' -OutFile 'mingw.7z'}"

if not exist mingw.7z (
    echo Download failed! Please manually download MinGW-w64
    echo Download URL: https://github.com/niXman/mingw-builds-binaries/releases
    pause
    exit /b 1
)

echo Extracting...
REM Extract using PowerShell (built-in Windows 10+)
powershell -Command "Expand-Archive -Path 'mingw.7z' -DestinationPath 'C:\' -Force"

if not exist "C:\mingw64\bin\g++.exe" (
    echo Extraction failed! Please manually extract mingw.7z to C:\
    pause
    exit /b 1
)

echo Setting environment variables...
REM Add to PATH
setx PATH "%PATH%;C:\mingw64\bin" /M

echo.
echo Installation complete!
echo MinGW installed to: C:\mingw64
echo.
echo Please reopen command prompt, then run:
echo compile_mingw.bat
echo.
pause

cd ..
rmdir /s /q temp
