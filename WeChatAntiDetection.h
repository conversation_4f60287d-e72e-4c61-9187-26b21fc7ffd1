#pragma once
#ifndef WECHAT_ANTI_DETECTION_H
#define WECHAT_ANTI_DETECTION_H

#include <windows.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <thread>
#include <chrono>

// 系统信息结构体
struct SystemInfo {
    std::string cpuName;
    std::string motherboardModel;
    std::string biosVersion;
    std::string macAddress;
    std::string diskModel;
    std::string systemManufacturer;
};

// 备份信息结构体
struct BackupInfo {
    std::map<std::string, std::string> registryBackup;
    SystemInfo originalSystemInfo;
    std::vector<std::string> modifiedFiles;
};

class WeChatAntiDetection {
private:
    BackupInfo backupData;
    bool isApplied;
    std::string logFile;
    
    // 核心功能模块
    bool HideVirtualizationFeatures();
    bool CleanVirtualizationTraces();
    
    // 辅助功能
    bool SetRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName, const std::string& value);
    std::string GetRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName);
    bool BackupRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName);
    
    // 硬件信息修改
    bool ModifyCPUInfo();
    bool ModifyMotherboardInfo();
    bool ModifyBIOSInfo();
    bool ModifyMACAddress();
    
    // 虚拟化特征隐藏
    bool HideKVMFeatures();
    bool HideXenFeatures();
    bool HideVMwareFeatures();
    bool HideVirtualBoxFeatures();
    bool HideHyperVFeatures();
    
    // 系统服务管理
    bool StopVirtualizationServices();

    // 工具函数
    std::string GenerateRandomString(int length);
    std::string GenerateRandomMACAddress();
    bool IsRunningAsAdmin();
    void WriteLog(const std::string& message);

    // 检测函数
    std::vector<std::string> GetVirtualizationIndicators();
    
public:
    WeChatAntiDetection();
    ~WeChatAntiDetection();
    
    // 主要接口
    bool Initialize();
    bool ApplyAntiDetection();
    bool RestoreOriginalSettings();
    bool IsAntiDetectionApplied() const;
    
    // 状态查询
    std::vector<std::string> GetCurrentStatus();
    std::string GetLastError() const;
    
    // 测试功能
    bool RunDetectionTest();
    std::vector<std::string> GetDetectionResults();
};

// 全局常量定义
namespace AntiDetectionConstants {
    // 注册表路径
    extern const std::string HARDWARE_KEY;
    extern const std::string BIOS_KEY;
    extern const std::string SYSTEM_KEY;
    extern const std::string NETWORK_KEY;

    // 虚拟化特征关键词
    extern const std::vector<std::string> VM_KEYWORDS;

    // 云服务商特征
    extern const std::vector<std::string> CLOUD_KEYWORDS;

    // 伪造的硬件信息
    extern const std::vector<std::string> FAKE_CPU_NAMES;
    extern const std::vector<std::string> FAKE_MOTHERBOARD_MODELS;
}

#endif // WECHAT_ANTI_DETECTION_H
