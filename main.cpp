#include "WeChatAntiDetection.h"
#include <iostream>
#include <string>
#include <conio.h>

void PrintMenu() {
    std::cout << "\n=== 微信防检测工具 ===" << std::endl;
    std::cout << "1. 初始化系统" << std::endl;
    std::cout << "2. 应用防检测措施" << std::endl;
    std::cout << "3. 恢复原始设置" << std::endl;
    std::cout << "4. 查看当前状态" << std::endl;
    std::cout << "5. 运行检测测试" << std::endl;
    std::cout << "6. 查看检测结果" << std::endl;
    std::cout << "0. 退出程序" << std::endl;
    std::cout << "请选择操作: ";
}

void PrintWarning() {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "                    重要警告" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "此工具将修改系统注册表和硬件信息显示。" << std::endl;
    std::cout << "请确保：" << std::endl;
    std::cout << "1. 您正在阿里云服务器上运行此程序" << std::endl;
    std::cout << "2. 您已经备份了重要数据" << std::endl;
    std::cout << "3. 您具有管理员权限" << std::endl;
    std::cout << "4. 您了解此操作的风险" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "按任意键继续，或按 Ctrl+C 退出..." << std::endl;
    _getch();
}

void PrintStatus(const std::vector<std::string>& status) {
    std::cout << "\n=== 当前状态 ===" << std::endl;
    for (const auto& line : status) {
        std::cout << line << std::endl;
    }
}

void PrintDetectionResults(const std::vector<std::string>& results) {
    std::cout << "\n=== 检测结果 ===" << std::endl;
    if (results.empty()) {
        std::cout << "✓ 未发现虚拟化特征，防检测效果良好！" << std::endl;
    } else {
        std::cout << "⚠ 发现以下虚拟化特征：" << std::endl;
        for (const auto& result : results) {
            std::cout << "  - " << result << std::endl;
        }
    }
}

int main() {
    // 设置控制台编码为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    std::cout << "微信防检测工具 v1.0" << std::endl;
    std::cout << "专为阿里云服务器设计" << std::endl;
    
    PrintWarning();
    
    WeChatAntiDetection antiDetection;
    bool initialized = false;
    
    while (true) {
        PrintMenu();
        
        int choice;
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                std::cout << "\n正在初始化系统..." << std::endl;
                if (antiDetection.Initialize()) {
                    std::cout << "✓ 系统初始化成功！" << std::endl;
                    initialized = true;
                } else {
                    std::cout << "✗ 系统初始化失败！请检查管理员权限。" << std::endl;
                }
                break;
            }
            
            case 2: {
                if (!initialized) {
                    std::cout << "请先初始化系统！" << std::endl;
                    break;
                }
                
                std::cout << "\n正在应用防检测措施..." << std::endl;
                std::cout << "这可能需要几分钟时间，请耐心等待..." << std::endl;
                
                if (antiDetection.ApplyAntiDetection()) {
                    std::cout << "✓ 防检测措施应用成功！" << std::endl;
                    std::cout << "建议重启系统以确保所有更改生效。" << std::endl;
                } else {
                    std::cout << "⚠ 部分防检测措施应用失败，请查看日志文件。" << std::endl;
                }
                break;
            }
            
            case 3: {
                std::cout << "\n正在恢复原始设置..." << std::endl;
                if (antiDetection.RestoreOriginalSettings()) {
                    std::cout << "✓ 原始设置恢复成功！" << std::endl;
                } else {
                    std::cout << "⚠ 部分设置恢复失败，请查看日志文件。" << std::endl;
                }
                break;
            }
            
            case 4: {
                auto status = antiDetection.GetCurrentStatus();
                PrintStatus(status);
                break;
            }
            
            case 5: {
                std::cout << "\n正在运行检测测试..." << std::endl;
                if (antiDetection.RunDetectionTest()) {
                    std::cout << "✓ 检测测试通过！未发现虚拟化特征。" << std::endl;
                } else {
                    std::cout << "⚠ 检测测试发现虚拟化特征，建议查看详细结果。" << std::endl;
                }
                break;
            }
            
            case 6: {
                auto results = antiDetection.GetDetectionResults();
                PrintDetectionResults(results);
                break;
            }
            
            case 0: {
                std::cout << "\n感谢使用微信防检测工具！" << std::endl;
                if (antiDetection.IsAntiDetectionApplied()) {
                    std::cout << "注意：防检测措施仍在生效中。" << std::endl;
                    std::cout << "如需恢复原始设置，请重新运行程序选择恢复选项。" << std::endl;
                }
                return 0;
            }
            
            default: {
                std::cout << "无效选择，请重新输入。" << std::endl;
                break;
            }
        }
        
        std::cout << "\n按任意键继续..." << std::endl;
        _getch();
        system("cls"); // 清屏
    }
    
    return 0;
}
