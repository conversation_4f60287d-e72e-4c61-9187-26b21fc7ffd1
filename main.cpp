#include "WeChatAntiDetection.h"
#include <iostream>
#include <string>
#include <conio.h>

void PrintMenu() {
    std::cout << "\n=== WeChat Anti-Detection Tool ===" << std::endl;
    std::cout << "1. Initialize System" << std::endl;
    std::cout << "2. Apply Anti-Detection Measures" << std::endl;
    std::cout << "3. Restore Original Settings" << std::endl;
    std::cout << "4. View Current Status" << std::endl;
    std::cout << "5. Run Detection Test" << std::endl;
    std::cout << "6. View Detection Results" << std::endl;
    std::cout << "0. Exit Program" << std::endl;
    std::cout << "Please select an option: ";
}

void PrintWarning() {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "                  IMPORTANT WARNING" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "This tool will modify system registry and hardware info." << std::endl;
    std::cout << "Please ensure:" << std::endl;
    std::cout << "1. You are running this on Alibaba Cloud server" << std::endl;
    std::cout << "2. You have backed up important data" << std::endl;
    std::cout << "3. You have administrator privileges" << std::endl;
    std::cout << "4. You understand the risks of this operation" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "Press any key to continue, or Ctrl+C to exit..." << std::endl;
    _getch();
}

void PrintStatus(const std::vector<std::string>& status) {
    std::cout << "\n=== Current Status ===" << std::endl;
    for (const auto& line : status) {
        std::cout << line << std::endl;
    }
}

void PrintDetectionResults(const std::vector<std::string>& results) {
    std::cout << "\n=== Detection Results ===" << std::endl;
    if (results.empty()) {
        std::cout << "✓ No virtualization features detected, anti-detection working well!" << std::endl;
    } else {
        std::cout << "⚠ Found the following virtualization features:" << std::endl;
        for (const auto& result : results) {
            std::cout << "  - " << result << std::endl;
        }
    }
}

int main() {
    // Set console encoding to UTF-8
    SetConsoleOutputCP(CP_UTF8);

    std::cout << "WeChat Anti-Detection Tool v1.0" << std::endl;
    std::cout << "Designed for Alibaba Cloud Servers" << std::endl;
    
    PrintWarning();
    
    WeChatAntiDetection antiDetection;
    bool initialized = false;
    
    while (true) {
        PrintMenu();
        
        int choice;
        std::cin >> choice;
        
        switch (choice) {
            case 1: {
                std::cout << "\nInitializing system..." << std::endl;
                if (antiDetection.Initialize()) {
                    std::cout << "✓ System initialization successful!" << std::endl;
                    initialized = true;
                } else {
                    std::cout << "✗ System initialization failed! Please check administrator privileges." << std::endl;
                }
                break;
            }

            case 2: {
                if (!initialized) {
                    std::cout << "Please initialize system first!" << std::endl;
                    break;
                }

                std::cout << "\nApplying anti-detection measures..." << std::endl;
                std::cout << "This may take a few minutes, please wait..." << std::endl;

                if (antiDetection.ApplyAntiDetection()) {
                    std::cout << "✓ Anti-detection measures applied successfully!" << std::endl;
                    std::cout << "Recommend restarting system to ensure all changes take effect." << std::endl;
                } else {
                    std::cout << "⚠ Some anti-detection measures failed, please check log file." << std::endl;
                }
                break;
            }

            case 3: {
                std::cout << "\nRestoring original settings..." << std::endl;
                if (antiDetection.RestoreOriginalSettings()) {
                    std::cout << "✓ Original settings restored successfully!" << std::endl;
                } else {
                    std::cout << "⚠ Some settings failed to restore, please check log file." << std::endl;
                }
                break;
            }
            
            case 4: {
                auto status = antiDetection.GetCurrentStatus();
                PrintStatus(status);
                break;
            }
            
            case 5: {
                std::cout << "\nRunning detection test..." << std::endl;
                if (antiDetection.RunDetectionTest()) {
                    std::cout << "✓ Detection test passed! No virtualization features found." << std::endl;
                } else {
                    std::cout << "⚠ Detection test found virtualization features, recommend checking detailed results." << std::endl;
                }
                break;
            }

            case 6: {
                auto results = antiDetection.GetDetectionResults();
                PrintDetectionResults(results);
                break;
            }

            case 0: {
                std::cout << "\nThank you for using WeChat Anti-Detection Tool!" << std::endl;
                if (antiDetection.IsAntiDetectionApplied()) {
                    std::cout << "Note: Anti-detection measures are still active." << std::endl;
                    std::cout << "To restore original settings, please run the program again and select restore option." << std::endl;
                }
                return 0;
            }

            default: {
                std::cout << "Invalid selection, please try again." << std::endl;
                break;
            }
        }

        std::cout << "\nPress any key to continue..." << std::endl;
        _getch();
        system("cls"); // Clear screen
    }
    
    return 0;
}
