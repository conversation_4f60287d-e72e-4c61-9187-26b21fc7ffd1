@echo off
chcp 65001 >nul
echo 使用MinGW编译微信防检测工具...

REM 检查是否存在g++编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到g++编译器
    echo.
    echo 请下载并安装MinGW-w64:
    echo 1. 访问: https://github.com/niXman/mingw-builds-binaries/releases
    echo 2. 下载: x86_64-*-win32-seh-*.7z
    echo 3. 解压到 C:\mingw64
    echo 4. 添加 C:\mingw64\bin 到PATH环境变量
    echo.
    pause
    exit /b 1
)

echo 正在编译...

REM 使用g++编译
g++ -std=c++17 -O2 -static-libgcc -static-libstdc++ ^
    main.cpp WeChatAntiDetection.cpp ^
    -o WeChatAntiDetection.exe ^
    -ladvapi32 -liphlpapi -lws2_32

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件: WeChatAntiDetection.exe
echo 文件大小: 
dir WeChatAntiDetection.exe | findstr WeChatAntiDetection.exe
echo.
echo 重要提醒:
echo 1. 必须以管理员身份运行程序
echo 2. 仅在阿里云服务器上使用
echo 3. 使用前请备份重要数据
echo.
pause
