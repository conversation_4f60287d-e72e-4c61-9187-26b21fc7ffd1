@echo off
echo Compiling WeChat Anti-Detection Tool using MinGW...

REM Check if g++ compiler exists
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: g++ compiler not found
    echo.
    echo Please download and install MinGW-w64:
    echo 1. Visit: https://github.com/niXman/mingw-builds-binaries/releases
    echo 2. Download: x86_64-*-win32-seh-*.7z
    echo 3. Extract to C:\mingw64
    echo 4. Add C:\mingw64\bin to PATH environment variable
    echo.
    pause
    exit /b 1
)

echo Compiling...

REM Compile using g++
g++ -std=c++17 -O2 -static-libgcc -static-libstdc++ ^
    main.cpp WeChatAntiDetection.cpp ^
    -o WeChatAntiDetection.exe ^
    -ladvapi32 -liphlpapi -lws2_32

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo Executable: WeChatAntiDetection.exe
echo File size:
dir WeChatAntiDetection.exe | findstr WeChatAntiDetection.exe
echo.
echo Important reminders:
echo 1. Must run as Administrator
echo 2. Use only on Alibaba Cloud servers
echo 3. Backup important data before use
echo.
pause
