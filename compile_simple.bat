@echo off
echo Simple compilation of WeChat Anti-Detection Tool...

REM Check if Visual Studio compiler exists
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Visual Studio compiler not found
    echo Please use "Developer Command Prompt for VS" to run this script
    echo Or install Visual Studio Build Tools
    pause
    exit /b 1
)

echo Compiling...

REM Compile directly using cl compiler
cl /EHsc /O2 /std:c++17 main.cpp WeChatAntiDetection.cpp ^
   /link advapi32.lib iphlpapi.lib ws2_32.lib ^
   /out:WeChatAntiDetection.exe

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo Executable: WeChatAntiDetection.exe
echo.
echo Important reminders:
echo 1. Must run as Administrator
echo 2. Use only on Alibaba Cloud servers
echo 3. Backup important data before use
echo.
pause
