@echo off
echo 简单编译微信防检测工具...

REM 检查是否存在Visual Studio编译器
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Visual Studio编译器
    echo 请使用 "Developer Command Prompt for VS" 运行此脚本
    echo 或者安装Visual Studio Build Tools
    pause
    exit /b 1
)

echo 正在编译...

REM 直接使用cl编译器编译
cl /EHsc /O2 /std:c++17 main.cpp WeChatAntiDetection.cpp ^
   /link advapi32.lib iphlpapi.lib ws2_32.lib ^
   /out:WeChatAntiDetection.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件: WeChatAntiDetection.exe
echo.
echo 重要提醒:
echo 1. 必须以管理员身份运行程序
echo 2. 仅在阿里云服务器上使用
echo 3. 使用前请备份重要数据
echo.
pause
