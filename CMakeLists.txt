cmake_minimum_required(VERSION 3.10)
project(WeChatAntiDetection)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器标志
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
endif()

# 添加可执行文件
add_executable(WeChatAntiDetection
    main.cpp
    WeChatAntiDetection.cpp
)

# 链接Windows库
if(WIN32)
    target_link_libraries(WeChatAntiDetection
        advapi32
        iphlpapi
        ws2_32
    )
endif()

# 设置输出目录
set_target_properties(WeChatAntiDetection PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 如果是Release模式，启用优化
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    if(MSVC)
        set_target_properties(WeChatAntiDetection PROPERTIES
            LINK_FLAGS "/SUBSYSTEM:CONSOLE"
        )
    endif()
endif()
