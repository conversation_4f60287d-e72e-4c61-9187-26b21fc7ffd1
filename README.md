# 微信防检测工具

专为阿里云服务器设计的微信虚拟机检测规避工具。

## ⚠️ 重要警告

**此工具仅供学习和研究目的使用。使用前请确保：**

1. 您正在自己的阿里云服务器上运行
2. 您已备份重要数据
3. 您了解修改系统信息的风险
4. 您遵守相关法律法规

## 🚀 功能特性

- **硬件信息伪装**: 修改CPU、主板、BIOS等硬件信息
- **虚拟化特征隐藏**: 隐藏KVM、Xen、VMware等虚拟化特征
- **MAC地址修改**: 生成真实厂商的MAC地址
- **服务管理**: 停止虚拟化相关服务
- **注册表清理**: 清理虚拟化痕迹
- **备份恢复**: 支持恢复原始设置
- **检测测试**: 内置虚拟化检测测试

## 📋 系统要求

- Windows Server 2016/2019/2022 (阿里云ECS)
- 管理员权限
- Visual Studio 2019 或更高版本 (编译需要)
- CMake 3.10 或更高版本

## 🔧 编译安装

### 方法一：使用批处理文件 (推荐)

1. 确保已安装Visual Studio
2. 双击运行 `build.bat`
3. 等待编译完成

### 方法二：手动编译

```bash
# 创建构建目录
mkdir build
cd build

# 生成项目文件
cmake .. -G "Visual Studio 16 2019" -A x64

# 编译
cmake --build . --config Release
```

## 📖 使用说明

### 1. 运行程序

**必须以管理员身份运行！**

```bash
# 进入编译输出目录
cd build\bin\Release

# 以管理员身份运行
WeChatAntiDetection.exe
```

### 2. 操作流程

1. **初始化系统** - 备份当前系统信息
2. **应用防检测措施** - 修改系统信息隐藏虚拟化特征
3. **运行检测测试** - 验证防检测效果
4. **查看检测结果** - 查看详细检测信息

### 3. 菜单选项说明

- `1. 初始化系统`: 备份当前系统信息，必须首先执行
- `2. 应用防检测措施`: 应用所有防检测修改
- `3. 恢复原始设置`: 恢复到修改前的状态
- `4. 查看当前状态`: 显示当前系统信息
- `5. 运行检测测试`: 测试是否还有虚拟化特征
- `6. 查看检测结果`: 显示详细的检测结果

## 🔍 工作原理

### 主要修改内容

1. **CPU信息伪装**
   - 修改注册表中的CPU名称
   - 使用真实的Intel/AMD CPU型号

2. **主板信息修改**
   - 更改系统制造商信息
   - 修改主板型号为真实品牌

3. **BIOS信息伪装**
   - 修改BIOS版本信息
   - 隐藏虚拟化相关标识

4. **网络适配器处理**
   - 生成真实厂商的MAC地址
   - 避免使用虚拟化厂商的OUI前缀

5. **服务和进程管理**
   - 停止虚拟化相关服务
   - 隐藏虚拟化进程

## 📝 日志文件

程序运行时会生成 `anti_detection_log.txt` 日志文件，记录：
- 操作时间戳
- 执行的操作
- 成功/失败状态
- 错误信息

## 🔄 恢复原始设置

如果需要恢复到修改前的状态：

1. 运行程序
2. 选择 `3. 恢复原始设置`
3. 等待恢复完成
4. 重启系统

## ⚡ 性能优化

- 使用C++编写，运行效率高
- 直接操作系统API，响应速度快
- 最小化系统资源占用
- 支持批量操作，减少执行时间

## 🛡️ 安全注意事项

1. **备份重要数据**: 修改前请备份重要文件
2. **测试环境**: 建议先在测试环境验证
3. **管理员权限**: 必须以管理员身份运行
4. **系统重启**: 某些修改需要重启后生效
5. **日志监控**: 定期检查日志文件

## 🔧 故障排除

### 常见问题

**Q: 提示"Administrator privileges required"**
A: 请以管理员身份运行程序

**Q: 编译失败**
A: 检查是否安装了Visual Studio和CMake

**Q: 某些设置修改失败**
A: 检查日志文件，可能需要额外的权限

**Q: 恢复设置失败**
A: 手动检查注册表备份，或重新安装系统

### 日志分析

查看 `anti_detection_log.txt` 文件了解详细错误信息：
- `ERROR`: 严重错误，操作失败
- `WARNING`: 警告信息，部分功能可能受影响
- `INFO`: 一般信息，操作正常

## 📞 技术支持

如遇到技术问题，请提供：
1. 操作系统版本
2. 错误信息截图
3. 日志文件内容
4. 具体操作步骤

## 📄 免责声明

本工具仅供学习研究使用，使用者需自行承担使用风险。开发者不对因使用本工具造成的任何损失负责。

---

**版本**: v1.0  
**更新时间**: 2025年1月  
**适用平台**: Windows Server (阿里云ECS)
