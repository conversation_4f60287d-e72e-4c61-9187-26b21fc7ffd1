@echo off
echo Compiling WeChat Anti-Detection Tool...

REM Check if Visual Studio exists
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Visual Studio compiler not found
    echo Please install Visual Studio or use Developer Command Prompt
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

REM Generate project files with CMake
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo ERROR: CMake configuration failed
    pause
    exit /b 1
)

REM Build project
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo Executable location: build\bin\Release\WeChatAntiDetection.exe
echo.
echo Usage instructions:
echo 1. Run WeChatAntiDetection.exe as Administrator
echo 2. Follow menu prompts
echo 3. Recommend running detection test first
echo.
pause
