@echo off
echo 正在编译微信防检测工具...

REM 检查是否存在Visual Studio
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Visual Studio编译器
    echo 请安装Visual Studio或使用Developer Command Prompt
    pause
    exit /b 1
)

REM 创建构建目录
if not exist build mkdir build
cd build

REM 使用CMake生成项目文件
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo 错误: CMake配置失败
    pause
    exit /b 1
)

REM 编译项目
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件位置: build\bin\Release\WeChatAntiDetection.exe
echo.
echo 使用说明:
echo 1. 以管理员身份运行 WeChatAntiDetection.exe
echo 2. 按照菜单提示操作
echo 3. 建议先运行检测测试查看当前状态
echo.
pause
